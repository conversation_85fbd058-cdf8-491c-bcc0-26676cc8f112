import React from "react";
import Container from "../Container";

// Hero Section Component with Bento Layout
export function Hero() {
  return (
    <section className="py-16 sm:py-24 bg-background" aria-labelledby="hero-heading">
      <Container>
        {/* Bento Grid Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 lg:gap-6 min-h-[600px]">
          {/* Top Row - 3 Big Cards */}
          {/* Main Hero Card */}
          <div className="lg:col-span-2 bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl p-6 lg:p-8 flex flex-col justify-center border border-primary/20">
            <div className="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium gap-2 w-fit mb-4" role="status" aria-label="New template announcement">
              <span className="inline-block h-2 w-2 rounded-full bg-emerald-500" aria-hidden="true" />
              New template
            </div>
            <h1 id="hero-heading" className="text-2xl lg:text-3xl xl:text-4xl font-heading font-semibold tracking-tight mb-4">
              Jadikan brand Anda menonjol
            </h1>
            <p className="text-sm lg:text-base text-muted-foreground mb-6">
              Template responsif, cepat, dan mudah dikustomisasi
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <a
                href="#contact"
                className="inline-flex h-10 items-center justify-center rounded-md bg-primary-button-bg text-primary-button-text px-4 text-sm font-medium hover:bg-primary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary-button-bg"
                aria-label="Start using the template now"
              >
                Mulai Sekarang
              </a>
              <a
                href="#gallery"
                className="inline-flex h-10 items-center justify-center rounded-md border border-secondary-button-border bg-secondary-button-bg text-secondary-button-text px-4 text-sm font-medium hover:bg-secondary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-secondary-button-border"
                aria-label="View template demo and examples"
              >
                Lihat Demo
              </a>
            </div>
          </div>

          {/* Features Card */}
          <div className="lg:col-span-2 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-2xl p-6 lg:p-8 border border-blue-200/50 dark:border-blue-800/50">
            <div className="h-full flex flex-col justify-between">
              <div>
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-blue-100 dark:bg-blue-900/50 mb-4">
                  <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-heading font-semibold mb-2">Performa Tinggi</h3>
                <p className="text-sm text-muted-foreground">
                  Dibangun dengan Next.js 15 dan optimasi modern untuk kecepatan maksimal
                </p>
              </div>
              <div className="mt-6 flex items-center text-sm text-blue-600 dark:text-blue-400 font-medium">
                <span>SEO-friendly</span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </div>

          {/* Stats Card */}
          <div className="lg:col-span-2 bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-950/20 dark:to-green-950/20 rounded-2xl p-6 lg:p-8 border border-emerald-200/50 dark:border-emerald-800/50">
            <div className="h-full flex flex-col justify-between">
              <div>
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-emerald-100 dark:bg-emerald-900/50 mb-4">
                  <svg className="w-6 h-6 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="space-y-3">
                  <div>
                    <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">99%</div>
                    <div className="text-xs text-muted-foreground">Performance Score</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">100%</div>
                    <div className="text-xs text-muted-foreground">Accessibility</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Row - 2 Middle + 1 Little */}
          {/* Customization Card */}
          <div className="lg:col-span-2 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
            <div className="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-purple-100 dark:bg-purple-900/50 mb-3">
              <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
              </svg>
            </div>
            <h3 className="text-lg font-heading font-semibold mb-2">Mudah Dikustomisasi</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Tailwind CSS memudahkan styling yang konsisten dan fleksibel
            </p>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-purple-400"></div>
              <div className="w-3 h-3 rounded-full bg-pink-400"></div>
              <div className="w-3 h-3 rounded-full bg-blue-400"></div>
              <span className="text-xs text-muted-foreground ml-2">Custom themes</span>
            </div>
          </div>

          {/* Responsive Card */}
          <div className="lg:col-span-2 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20 rounded-2xl p-6 border border-orange-200/50 dark:border-orange-800/50">
            <div className="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-orange-100 dark:bg-orange-900/50 mb-3">
              <svg className="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-heading font-semibold mb-2">Responsif</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Tampilan optimal di semua perangkat dan ukuran layar
            </p>
            <div className="flex items-center gap-1">
              <div className="w-4 h-3 bg-orange-300 rounded-sm"></div>
              <div className="w-3 h-4 bg-orange-400 rounded-sm"></div>
              <div className="w-5 h-3 bg-orange-500 rounded-sm"></div>
              <span className="text-xs text-muted-foreground ml-2">All devices</span>
            </div>
          </div>

          {/* Small CTA Card */}
          <div className="lg:col-span-2 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/50 dark:to-gray-800/50 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/50 flex flex-col justify-center items-center text-center">
            <div className="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-gray-200 dark:bg-gray-700 mb-3">
              <svg className="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 className="text-sm font-heading font-semibold mb-2">Siap Pakai</h3>
            <p className="text-xs text-muted-foreground mb-3">
              Template lengkap untuk bisnis modern
            </p>
            <a
              href="#contact"
              className="inline-flex h-8 items-center justify-center rounded-md bg-gray-900 dark:bg-gray-100 text-gray-100 dark:text-gray-900 px-3 text-xs font-medium hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors"
            >
              Mulai
            </a>
          </div>
        </div>
      </Container>
    </section>
  );
}

// Key Benefits Section Component
const benefits = [
  {
    title: "Cepat & SEO-friendly",
    desc: "Dibangun dengan Next.js 15 dan optimasi modern.",
  },
  {
    title: "Mudah dikustomisasi",
    desc: "Tailwind CSS memudahkan styling yang konsisten.",
  },
  {
    title: "Responsif",
    desc: "Tampilan optimal di semua perangkat.",
  },
];

export function KeyBenefits() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Key Benefits</h2>
        <div className="mt-8 grid gap-6 sm:grid-cols-3">
          {benefits.map((b) => (
            <div key={b.title} className="rounded-xl border p-6 bg-white/50 dark:bg-zinc-900/50">
              <h3 className="font-heading font-semibold text-lg">{b.title}</h3>
              <p className="mt-2 text-sm text-muted-foreground">{b.desc}</p>
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
}

// About Section Component
export function About() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Tentang Kami</h2>
        <div className="mt-4 grid gap-6 sm:grid-cols-2 items-start">
          <p className="text-sm text-muted-foreground">
            Kami adalah tim yang fokus pada pengalaman pengguna dan performa.
            Template ini dirancang untuk memudahkan Anda meluncurkan landing page berkualitas tinggi.
          </p>
          <div className="rounded-xl border p-6">
            <h3 className="font-heading font-semibold">Misi</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Membantu bisnis tumbuh lewat web yang cepat, indah, dan efektif.
            </p>
          </div>
        </div>
      </Container>
    </section>
  );
}

// Location & Hours Section Component
export function LocationHours() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Lokasi & Jam Operasional</h2>
        <div className="mt-6 grid gap-6 sm:grid-cols-2">
          <div className="rounded-xl border p-6">
            <h3 className="font-heading font-semibold">Alamat</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Jl. Contoh No. 123, Jakarta, Indonesia
            </p>
            <div className="mt-4">
              <a
                href="#"
                className="inline-flex h-10 items-center justify-center rounded-md border border-secondary-button-border bg-secondary-button-bg text-secondary-button-text px-4 text-sm hover:bg-secondary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-secondary-button-border"
              >
                Lihat di Google Maps
              </a>
            </div>
          </div>
          <div className="rounded-xl border p-6">
            <h3 className="font-heading font-semibold">Jam Operasional</h3>
            <ul className="mt-2 text-sm text-muted-foreground space-y-1">
              <li>Senin - Jumat: 09:00 - 18:00</li>
              <li>Sabtu: 10:00 - 16:00</li>
              <li>Minggu & Hari Libur: Tutup</li>
            </ul>
          </div>
        </div>
      </Container>
    </section>
  );
}
